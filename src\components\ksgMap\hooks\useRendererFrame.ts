/**
 * 渲染帧管理器 - KsgMap 3D 场景渲染循环核心模块
 *
 * 该模块负责管理 Three.js 场景的渲染循环，协调各个渲染组件的更新
 * 是 KsgMap 可视化系统的渲染引擎，确保场景的流畅显示和交互响应
 */

import ctx from "../ctx";
import TWEEN from "@tweenjs/tween.js";
import { Clock } from "three";
import KsgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { hoverLabel } from "../core/KsgLabel";
import type { Point } from "../types";

/**
 * 渲染帧 Hook - 管理 3D 场景的渲染循环和动画更新
 *
 * 功能特性：
 * - 统一管理场景渲染循环
 * - 协调多个渲染器的更新（WebGL、CSS2D）
 * - 处理动画系统更新（Tween、节点动画、焦点动画）
 * - 管理相机控制器更新
 * - 提供稳定的 60FPS 渲染性能
 *
 * @returns 包含渲染循环启动方法的对象
 */
export default function useRenderFrame() {
  // Three.js 时钟对象 - 用于计算每帧的时间间隔，确保动画的时间一致性
  const clock = new Clock();

  // 用于跟踪当前自动旋转时显示label的点索引
  let currentAutoRotateHoverIndex: number = -1;
  // 性能优化：限制检测频率，避免每帧都检测
  let frameCount = 0;
  const CHECK_INTERVAL = 3; // 每3帧检测一次

  /**
   * 检测最靠近屏幕中心的点
   * 在自动旋转时调用，找到距离屏幕中心最近的可见点
   */
  function findClosestPointToScreenCenter(): { point: Point; index: number } | null {
    if (!ctx.pointsMesh || !ctx.camera || !ctx.renderer || !ctx.graph?.pointsData) return null;

    // 获取屏幕中心坐标
    const screenCenter = {
      x: ctx.renderer.domElement.clientWidth / 2,
      y: ctx.renderer.domElement.clientHeight / 2
    };

    let closestPoint = null;
    let minDistance = Infinity;
    let closestIndex = -1;

    // 设置一个合理的距离阈值，避免显示距离屏幕中心太远的点
    const MAX_DISTANCE_THRESHOLD = Math.min(screenCenter.x, screenCenter.y) * 0.3; // 屏幕尺寸的30%

    // 遍历所有点，找到距离屏幕中心最近的点
    ctx.graph.pointsData.forEach((point) => {
      if (point.index === undefined) return; // 跳过没有索引的点

      try {
        // 获取点在屏幕上的坐标
        const screenPos = ctx.pointsMesh!.getWorldP(
          point.index,
          ctx.camera!,
          ctx.renderer!.domElement.clientWidth,
          ctx.renderer!.domElement.clientHeight
        );

        // 计算到屏幕中心的距离
        const distance = Math.sqrt(
          Math.pow(screenPos.x - screenCenter.x, 2) +
          Math.pow(screenPos.y - screenCenter.y, 2)
        );

        // 检查点是否在屏幕可视范围内且距离合理
        if (screenPos.x >= 0 && screenPos.x <= screenCenter.x * 2 &&
            screenPos.y >= 0 && screenPos.y <= screenCenter.y * 2 &&
            distance < minDistance && distance <= MAX_DISTANCE_THRESHOLD) {
          minDistance = distance;
          closestPoint = point;
          closestIndex = point.index;
        }
      } catch (error) {
        // 忽略计算错误，继续处理下一个点
        console.warn('Error calculating screen position for point:', point.id, error);
      }
    });

    return closestPoint ? { point: closestPoint, index: closestIndex } : null;
  }

  /**
   * 启动渲染循环 - 核心渲染函数，每帧调用一次
   *
   * 渲染管道执行顺序：
   * 1. 计算帧时间间隔
   * 2. CSS2D 标签渲染（知识点标签）
   * 3. WebGL 主场景渲染
   * 4. 相机控制器更新
   * 5. 动画系统更新
   * 6. 自定义组件更新
   * 7. 递归调用下一帧
   *
   * @param time - 当前时间戳，由 requestAnimationFrame 提供
   */
  function startRenderFrame(time: any = 0) {
    // 计算自上一帧以来的时间间隔（秒）
    const deltaTime = clock.getDelta();

    // 注释：视椎体裁剪更新（可能用于性能优化）
    // updateVisible(ctx.viewGroup!, ctx.camera!);

    // CSS2D 渲染器 - 渲染知识点标签等 2D 元素
    ctx.css2dRenderer?.render(ctx.scene!, ctx.camera!);

    // 注释：后处理渲染管道（可能用于特效处理）
    // ctx.composer?.render();

    // 主 WebGL 渲染器 - 渲染 3D 场景
    ctx.renderer?.render(ctx.scene!, ctx.camera!);

    // 相机控制器更新 - 处理用户交互（旋转、缩放、平移）
    ctx.controls?.update(deltaTime);
    // 自动旋转更新（如果启用）
    ctx.controls?.autoRotateUpdate(deltaTime);

    // 自动旋转时显示最靠近屏幕中心的点的label
    frameCount++;
    if (ctx.controls?.autoRotate && !ctx.controls?.isControls) {
      // 性能优化：不是每帧都检测，减少计算量
      if (frameCount % CHECK_INTERVAL === 0) {
        const closestResult = findClosestPointToScreenCenter();
        if (closestResult && closestResult.point && closestResult.index !== currentAutoRotateHoverIndex) {
          // 隐藏之前的label
          if (currentAutoRotateHoverIndex !== -1) {
            hoverLabel.hide();
          }

          // 显示新的label
          const pointDnc = ctx.pointsMesh?.getWorldP(
            closestResult.point.index!,
            ctx.camera!,
            ctx.renderer!.domElement.clientWidth,
            ctx.renderer!.domElement.clientHeight
          );

          if (pointDnc && ctx.viewRange) {
            hoverLabel.display(closestResult.point, {
              viewRange: ctx.viewRange,
              dnc: pointDnc
            });
            currentAutoRotateHoverIndex = closestResult.index;
          }
        }
      }
    } else if (currentAutoRotateHoverIndex !== -1) {
      // 如果不在自动旋转状态，隐藏自动旋转的label
      hoverLabel.hide();
      currentAutoRotateHoverIndex = -1;
    }

    // Tween.js 动画系统更新 - 处理补间动画
    TWEEN.update(time);

    // 递归调用，实现连续渲染循环
    requestAnimationFrame(startRenderFrame);

    // 自定义组件更新
    // 节点悬停动画更新
    KsgHover.update(ctx, deltaTime);
    // 焦点外壳动画更新
    focusCrust.update(deltaTime);
    // 点云网格更新（如果存在）
    if (ctx.pointsMesh) ctx.pointsMesh.update();
    // 焦点连线更新（如果存在）
    if (ctx.focusLine) ctx.focusLine?.update();
  }

  // 返回渲染控制接口
  return {
    startRenderFrame, // 启动渲染循环的方法
  };
}
